<!-- eslint-disable max-len -->
<template>
  <q-page
    class="tw:w-full tw:h-[calc(100vh-11.5rem)]
    tw:tl:h-[calc(100vh-7.3rem)] tw:dt:h-[calc(100vh-6.3rem)]"
  >
    <q-card
      v-if="displayActualReceived"
      class=" tw:rounded-lg tw:tl:h-[7rem] tw:flex tw:flex-col tw:justify-center tw:mx-4 tw:tl:mx-0 tw:mb-3"
    >
      <div
        class="tw:flex tw:items-start tw:tl:items-center tw:justify-between
        tw:tl:flex-row tw:flex-col"
      >
        <div class="tw:flex tw:flex-1 tw:flex-col tw:items-start tw:ml-2 tw:pl-4">
          <div class="tw:flex">
            <div class="tw:text-m-design tw:mr-8 tw:font-[700]">
              {{ FORMAT_DATE_NOW("YYYY/MM/DD") }}
            </div>
            <div class="tw:mr-8 tw:text-m-design tw:font-[700]">入荷実績</div>
          </div>
          <div
            @click.prevent="resetExportToday"
            class="tw:my-3 tw:text-blue-3 tw:cursor-pointer tw:text-xs-design
            tw:font-bold tw:underline"
          >
            本日の入荷実績の表示をリセットする
          </div>
        </div>
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:tl:justify-end tw:items-center"
        >
          <div
            class="tw:flex tw:px-3 tw:tl:justify-end tw:items-center
            tw:hover:cursor-pointer tw:w-full "
            @click.prevent="gotoPage('arrivalList')"
          >
            <!-- vs11 done -->
            <div
              class="tw:flex tw:items-center tw:justify-center tw:mt-2 tw:mr-3"
            >
              <img :src="UserGroupSvg" class="tw:w-[36px] tw:h-[36px]" />
            </div>
            <div
              :class="`tw:text-[#333333]`"
              class="tw:items-center tw:flex tw:text-m-design tw:font-[700]"
            >
              <span class="tw:text-xxl-design">{{
                FORMAT_NUMBER(exportToday.persons)
              }}</span>
              <span class="tw:mt-6">人</span>
            </div>
          </div>
          <div
            class="tw:flex tw:px-3 tw:tl:justify-end
            tw:items-center tw:w-full"
          >
            <div
              class="tw:flex tw:w-[36px] tw:items-center tw:justify-center tw:mt-2 tw:mr-3"
            >
              <img :src="BagSvg" class="tw:w-[36px] tw:h-[36px]" />
            </div>
            <div
              :class="`tw:text-[#333333]`"
              class="tw:items-center tw:flex tw:text-m-design tw:font-[700]"
            >
              <span class="tw:text-xxl-design">{{
                FORMAT_NUMBER_WEIGHT(exportToday.weight)
              }}</span>
              <span class="tw:mt-6">g</span>
            </div>
          </div>
        </div>
      </div>
    </q-card>
    <q-card class=" tw:bg-white tw:rounded-lg tw:mt-[1rem] tw:flex tw:justify-center tw:flex-col tw:tl:h-[40.25rem] tw:m-4 tw:tl:m-0">
      <!-- image -->
      <div
        class="tw:flex tw:justify-center tw:flex-col
        tw:tl:flex-row tw:tl:justify-between tw: tw:relative tw:gap-y-6"
      >
        <div
          class="tw:flex tw:flex-col tw:tl:w-[45.5%] tw:h-[20.125rem] tw:tl:my-8 tw:tl:mx-5.5 tw:tl:py-9 tw:tl:px-11.5
          tw:items-center tw:justify-center tw:rounded-[2rem] tw:m-2 tw:p-4 tw:hover:cursor-pointer tw:mx-4"
          :class="[
            user?.status === USER_STATUS_ENUM.NONACTIVE
              ? 'tw:bg-[#083B92] tw:opacity-30'
              : 'tw:bg-[#4D83E01A] tw:border-[0.5625rem] tw:border-[#083B92]',
            !CHECK_EXPIRY_DATE_LICENSE(
              user?.license?.expiry_date,
              settingUser.catching_reserve_period
            ) &&
            user?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
            user?.status !== USER_STATUS_ENUM.NONACTIVE
              ? 'tw:cursor-not-allowed'
              : 'tw:cursor-pointer',
              isDisabled ? 'tw:cursor-not-allowed tw:opacity-50 tw:pointer-events-none' : 'tw:cursor-pointer'
          ]" @click.prevent="handleChangePage('registerArrival')" v-if="
            CHECK_ROLE(
              [ROLES_ENUM.NORMAL_USER],
              [
                ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
                ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
                ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
              ],
              [],
              user
            )
          "
        >
          <div>
            <img
              v-if="user?.status === USER_STATUS_ENUM.NONACTIVE"
              :src="QRDisableSvg"
              class="tw:w-[6rem] tw:h-[6rem]"
            />
            <img
              v-else
              :src="QRSvg"
              class="tw:w-[6rem] tw:h-[6rem]"
            />
          </div>
          <div
            class="tw:text-[#083B92] tw:text-xl-design tw:font-[700]
            tw:max-[380px]:ml-0 tw:ml-4 tw:tl:ml-0"
            :class="[
              user?.status === USER_STATUS_ENUM.NONACTIVE
                ? 'tw:text-white'
                : 'tw:text-[#083B92]',
            ]"
          >
            入荷登録をする
          </div>
        </div>
        <div
          :class="[
            'tw:flex tw:items-center tw:justify-center tw:flex-col tw:tl:w-[45.5%] tw:h-[20.125rem] tw:tl:my-8 tw:tl:mx-5.5 tw:tl:py-9 tw:tl:px-11.5 tw:rounded-[2rem] tw:tl:m-2 tw:p-4 tw:m-4',
            user?.status === USER_STATUS_ENUM.NONACTIVE
              ? 'tw:bg-[#C74700] tw:opacity-30'
              : 'tw:bg-[#C747000D] tw:border-[0.5625rem] tw:border-[#C74700]',
            !CHECK_EXPIRY_DATE_LICENSE(
              user?.license?.expiry_date,
              settingUser.catching_reserve_period
            ) &&
            user?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
            user?.status !== USER_STATUS_ENUM.NONACTIVE
            ? 'tw:cursor-not-allowed'
            : 'tw:cursor-pointer',
            isDisabled ? 'tw:cursor-not-allowed tw:opacity-50 tw:pointer-events-none' : 'tw:cursor-pointer'
        ]" @click.prevent=" handleChangePage('registerOutboundShipment')" v-if="
            CHECK_ROLE(
              [ROLES_ENUM.NORMAL_USER],
              [
                ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
                ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
              ],
              [],
              user
            )
        ">
          <div>
            <img
              v-if="user?.status === USER_STATUS_ENUM.NONACTIVE"
              :src="CarShipmentDisableSvg"
              class="tw:w-[6rem] tw:h-[6rem]"
            />
            <img
              v-else
              :src="CarShipmentSvg"
              class="tw:w-[6rem] tw:h-[6rem]"
            />
          </div>
          <div
            class="tw:text-xl-design tw:font-[700] tw:max-[380px]:ml-0 tw:ml-4 tw:tl:ml-0"
            :class="[
              user?.status === USER_STATUS_ENUM.NONACTIVE
                ? 'tw:text-white'
                : 'tw:text-[#C74700]',
            ]"
          >
            出荷登録をする
          </div>
        </div>
        <div
          v-if="user?.status === USER_STATUS_ENUM.NONACTIVE"
          class="tw:absolute tw:left-1/2 tw:top-[25%] tw:tl:top-[29%]
          tw:translate-x-[-50%] tw:mt-4 tw:flex tw:justify-center tw:items-center tw:w-full"
        >
          <q-badge
            class="tw:border-[2px] tw:border-[#004AB9]
            tw:bg-[#EEF3FC] tw:rounded tw:px-4 tw:py-2 tw:h-[19rem] tw:tl:h-[8rem]
            tw:w-[72%] tw:tl:w-full tw:flex tw:justify-center tw:items-center"
          >
            <span
              class="tw:text-m-design tw:font-bold tw:text-[#083B92]
              tw:text-center tw:break-words tw:whitespace-normal"
            >
              入出荷に関する機能は管理者によって制限されています。
            </span>
          </q-badge>
        </div>
      </div>

      <div
        class="tw:tl:mx-8 tw:mb-4 tw:border-gray"
      >
        <div
          class="tw:grid tw:tl:grid-cols-3 tw:grid-cols-1 tw:gap-x-[3.2rem] tw:gap-y-6 tw:mt-4"
        >
          <div
            v-for="option in listOption"
            :key="option"
            @click.prevent="gotoPage(option.push)"
            class="tw:h-[5.5rem] tw:py-3 tw:px-4 tw:border tw:border-blue-3 tw:rounded-[3rem] tw:flex
            tw:items-center tw:justify-center tw:hover:cursor-pointer tw:mx-4 tw:tl:mx-0"
            :class="[
              CHECK_ROLE(option.role, option.type, option.staff, user)
                ? ''
                : 'tw:hidden',
              option.class,
            ]"
          >
            <img :src="option.svg" class="tw:w-[2.25rem] tw:h-[2.25rem]" />
            <div class="tw:font-black tw:text-m-design tw:ml-3 tw:text-[#004ab9]">
              {{ option.label }}
            </div>
          </div>
        </div>
      </div>
    </q-card>
  </q-page>
</template>

<script setup>
// vs17
import { computed, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import {
  ROLES_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  OPTION_TYPE_ENUM,
} from 'helpers/constants';
import {
  CHECK_ROLE,
  FORMAT_NUMBER,
  CHECK_EXPIRY_DATE_LICENSE,
  FORMAT_DATE_NOW,
  FORMAT_NUMBER_WEIGHT,
} from 'helpers/common';
import shippingService from 'services/shipping.service';
import { useAuthStore } from 'stores/auth-store';
import commonService from 'services/common.service';
import toast from 'utilities/toast';
import MESSAGE from 'helpers/message';
import CarShipmentSvg from 'assets/CarShipmentSvg.svg';
import CarShipmentDisableSvg from 'assets/CarShipmentDisableSvg.svg';
import QRDisableSvg from 'assets/QRDisableSvg.svg';
import QRSvg from 'assets/QRSvg.svg';
import UserGroupSvg from 'assets/UserGroupSvg.svg';
import BagSvg from 'assets/BagSvg.svg';
import InboundShipmentIcon from 'assets/InboundShipmentIcon.svg';
import RecordsShipmentIcon from 'assets/RecordsShipmentIcon.svg';
import InventoryIcon from 'assets/InventoryIcon.svg';
import ClientManagementIcon from 'assets/ClientManagementIcon.svg';
import UserIcon from 'assets/UserIcon.svg';
import {
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  USER_STATUS_ENUM,
} from 'src/helpers/constants';
import inventoryManagementService from 'src/shared/services/inventoryManagement.service';

const { settingUser } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const displayActualReceived = ref(false);
const router = useRouter();
const listOption = computed(() => {
  const options = [
    {
      svg: InboundShipmentIcon,
      label: '入荷実績管理',
      role: [ROLES_ENUM.NORMAL_USER],
      type: [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
      ],
      staff: [],
      push: 'arrivalList',
    },
    {
      svg: RecordsShipmentIcon,
      label: '出荷実績管理',
      role: [ROLES_ENUM.NORMAL_USER],
      type: [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
      ],
      staff: [],
      push: 'shippingList',
    },
    // TODO: Hide the 'HarvestReport' button
    // {
    //   svg: HarvestReportIcon,
    //   label: '採捕実績報告',
    //   push: 'manualRegistration',
    //   role: [ROLES_ENUM.NORMAL_USER],
    //   type: [
    //     ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
    //     ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
    //     ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
    //   ],
    //   staff: [],
    // },
    {
      svg: InventoryIcon,
      label: '在庫管理',
      role: [ROLES_ENUM.NORMAL_USER],
      type: [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
      ],
      staff: [],
      push: 'inventoryList',
    },
    {
      svg: ClientManagementIcon,
      label: '取引先管理',
      role: [ROLES_ENUM.NORMAL_USER],
      type: [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
      ],
      staff: [],
      push: 'partner',
    },
    {
      svg: UserIcon,
      label: '従事者管理',
      role: [ROLES_ENUM.NORMAL_USER],
      type: [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
      ],
      staff: [STAFF_TYPE_ENUM.ENTERPRISE],
      push: 'employeeList',
    },
  ];

  const MAX_ITEMS = 6;
  while (options.length < MAX_ITEMS) {
    options.push({
      svg: null,
      label: '',
      role: [],
      push: '',
      class: 'invisible',
    });
  }

  return options;
});

const exportToday = ref({
  persons: 0,
  weight: 0,
  date: '',
});

const isDisabled = computed(() =>
  user?.value.status === USER_STATUS_ENUM.NONACTIVE ? true : false);

const gotoPage = async name => {
  let routerName = name;
  if (name === 'registerOutboundShipment') {
    const supplierListResponse = await commonService.getOptions({
      type: OPTION_TYPE_ENUM.USER_SHIPPER,
    });
    const totalInventoryResponse =
      await inventoryManagementService.getTotalWeightInventory();

    if (supplierListResponse.payload.length === 0) {
      toast.error(MESSAGE.MSG_NA_CUSTOMER_ERR);
      return;
    }
    if (!totalInventoryResponse.payload.total_weight_inventory) {
      toast.error(MESSAGE.MSG_NONE_STOCK_ERROR);
      return;
    }

  }
  if (name === 'registerArrival') {
    switch (settingUser.value?.qr_scan_init) {
      case SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA:
        routerName = 'arrivalQrCamera';
        break;
      case SHOW_DEFAULT_SCAN_QR_ENUM.USE_SCAN:
        routerName = 'arrivalQrScan';
        break;
      default:
        routerName = 'loginInsteadUserId';
        break;
    }
  }
  await router.push({
    name: routerName,
  });
};

const handleChangePage = async name => {
  if (isDisabled.value) {
    return;
  }
  gotoPage(name);
};

const getExportToday = async () => {
  const result = await shippingService.getExportToday();
  exportToday.value.persons = result.payload.persons;
  exportToday.value.weight = result.payload.weight;
  exportToday.value.date = result.payload.date;
};

const resetExportToday = async () => {
  await shippingService.resetExportToday();
  await getExportToday();
};

onMounted(async () => {
  displayActualReceived.value = settingUser.value?.display_actual_received;
  if (
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
      [],
      user.value
    ) &&
    settingUser.value?.display_actual_received
  ) {
    await getExportToday();
  }
});
</script>
