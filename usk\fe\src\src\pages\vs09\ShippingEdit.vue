<template>
  <div>
    <PopupConfirm
      v-model="isShowPopupConfirmVolume"
      label="修正確認"
      description="以下の内容で、出荷実績を修正します"
      labelSubmit="登録する"
      labelCancel="入力内容を修正する"
      @onClickCancel="isShowPopupConfirmVolume = false"
      @onClickSubmit="handleClickSubmitVolume"
    >
      <div class="tw:text-m-design tw:p-5 tw:pt-0">
        <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-[#E0E0E0]">
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center">
            <span>
              出荷先
            </span>
          </div>
          <div class="tw:tl:w-[70%] tw:flex tw:gap-4 tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <span> {{ destinationForegin }} </span>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        >
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
            <span>
              出荷量
            </span>
            <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
              必須
            </q-badge>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <div class="tw:flex tw:tl:gap-12">
              <div class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row">
                <!-- 全体重量 -->
                <div>
                  <span class="tw:text-m-design">
                    {{ form.grossWeight }}
                  </span>
                  <span class="tw:text-xs-design"> g </span>
                </div>
              </div>
              <div class="tw:flex tw:items-center tw:relative">
                <!-- subtraction sign sm -->
                <div
                  class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-2 tw:mx-5 tw:tl:hidden"
                />
                <div class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3">
                  <!-- subtraction sign tl -->
                  <div
                    class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mx-5 tw:hidden
                      tw:tl:block tw:absolute tw:top-[1.5rem] tw:-left-[3.25rem]"
                  />
                  <!-- 風袋 -->
                  <div>
                    <span class="tw:text-m-design">
                      {{ form.tareWeight }}
                    </span>
                    <span class="tw:text-xs-design"> g </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="tw:flex tw:items-center tw:gap-4">
              <!-- 出荷量 -->
              <span class="tw:text-xs-design">出荷量</span>
              <div>
                <span class="tw:text-m-design">
                  {{ form.netWeight }}
                </span>
                <span class="tw:text-xs-design"> g </span>
              </div>
            </div>
          </div>
        </div>
        <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]">
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
            <span>
              出荷日
            </span>
            <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
              必須
            </q-badge>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]
            tw:flex tw:items-center tw:min-h-[4.25rem]">
            <span>
              {{ FORMAT_DATE(form.date) }}
            </span>
          </div>
        </div>
      </div>
    </PopupConfirm>
    <PopupConfirm
      v-model="isShowPopupConfirmReason"
      label="出荷量の差異"
      description="在庫量を大きく越える出荷量が指定されています。 差異発生の理由をチェックしてください"
      labelSubmit="差異の理由を登録する"
      labelCancel="出荷量を修正する"
      @onClickCancel="isShowPopupConfirmReason = false"
      @onClickSubmit="handleClickSubmitReason"
    >
      <div class="tw:text-m-design tw:p-5 tw:pt-0">
        <div>
            <div
              class="tw:flex tw:items-center"
            >
              <span> 差異の理由 </span>
              <q-badge
                class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
              >
                必須
              </q-badge>
            </div>
            <div
              class="tw:flex tw:gap-4 tw:py-1"
            >
              <q-radio
                v-for="(item, index) in reasonOptions"
                v-model="form.typeDiff"
                :key="index"
                :val="item.value"
                size="3.75rem"
                class="tw:transform tw:-translate-x-3"
              >
                <span class="tw:text-m-design tw:text-[#333333]">
                  {{ item.label }}
                </span>
              </q-radio>
            </div>
        </div>
        <div>
            <div
              class="tw:flex tw:items-center"
            >
              <span> 「その他」を選択した理由 </span>
              <q-badge
                class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
              >
                必須
              </q-badge>
            </div>
            <div
              class="tw:flex tw:gap-4 tw:py-1"
            >
              <q-input
                :class="[
                  {
                    'tw:bg-[#CACACA] tw:border tw:border-[#D2D2D2]':
                      form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
                  },
                  'tw:text-m-design tw:w-full',
                ]"
                :disable="form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER"
                v-model="form.reasonDiff"
                outlined
                autocomplete="nope"
                type="textarea"
                maxlength="300"
                no-error-icon
                hide-bottom-space
              />
            </div>
        </div>
      </div>
    </PopupConfirm>
    <q-page class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
      <q-card class="tw:p-5 tw:flex tw:flex-col tw:h-full">
        <div>
          <h2 class="tw:text-l-design tw:font-bold">出荷実績詳細</h2>
        </div>
        <div class="tw:text-m-design tw:mt-5">
          <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
            tw:border tw:border-[#E0E0E0]">
            <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center">
              <span>
                漁獲/荷口番号
              </span>
            </div>
            <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
              <span>{{ maskCodeString(data.code) }}</span><br/>
            </div>
          </div>
          <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
            tw:border tw:border-t-0 tw:border-[#E0E0E0]">
            <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
              <span>
                出荷先
              </span>
              <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
                必須
              </q-badge>
            </div>
            <div
            v-if="!checkDestinationForegin()"
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-10 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
              <div>
                <BasePartnerSelect
                  class="tw:font-normal tw:text-l-design"
                  :error-message="errors.destinationId"
                  :error="!!errors.destinationId"
                  v-model="form.destinationId"
                />
              </div>
            </div>
            <div
            v-if="checkDestinationForegin()"
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
              <span>{{ destinationForegin }}</span>
            </div>
          </div>
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
            tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
              <span>
                出荷量
              </span>
              <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
                必須
              </q-badge>
            </div>
            <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-11 tw:border-t-1 tw:tl:border-t-0
             tw:tl:border-l-1 tw:border-[#E0E0E0]">
              <div class="tw:grid tw:grid-cols-1 tw:gap-x-10 tw:gap-y-3 tw:tl:grid-cols-2">
                <div class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row">
                  <!-- 全体重量 -->
                  <span class="tw:text-xs-design tw:min-w-[6rem]">全体重量</span>
                  <BaseInput
                    clearable
                    type="text"
                    maxlength="10"
                    inputmode="decimal"
                    autocomplete="nope"
                    option-value="value"
                    :model-value="form.grossWeight"
                    @update:model-value="handleInputGrossWeight"
                    outlined
                    class="tw:flex-1"
                    input-class="tw:text-right tw:text-m-design"
                    :error-message="errors.grossWeight"
                    :error="!!errors.grossWeight"
                    :mask="{
                      mask: Number, // Chỉ chấp nhận số
                      thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                      scale: 2, // Không cho phép số thập phân
                      signed: false, // Không cho phép số âm
                      min: 0, // Chỉ cho phép số không âm
                      lazy: false, // Hiển thị placeholder ngay lập tức
                      radix: '.',
                      max: 99999.99,
                      padFractionalZeros: false,
                      normalizeZeros: true,
                    }"
                  >
                    <template v-slot:append>
                      <div
                        :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                      >
                        g
                      </div>
                    </template>
                  </BaseInput>
                </div>
                <div class="tw:flex tw:items-center tw:relative">
                  <!-- subtraction sign sm -->
                  <div
                    class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-12 tw:mx-5 tw:tl:hidden"
                  />
                  <div class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3">
                    <!-- subtraction sign tl -->
                    <div
                      class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden
                        tw:tl:block tw:absolute tw:top-[2rem] tw:-left-[3.25rem]"
                    />
                    <!-- 風袋 -->
                    <span class="tw:text-xs-design tw:min-w-[3rem]">風袋</span>
                    <BaseInput
                      clearable
                      type="text"
                      maxlength="10"
                      inputmode="decimal"
                      autocomplete="nope"
                      :model-value="form.tareWeight"
                      @update:model-value="handleInputTareWeight"
                      outlined
                      class="tw:flex-1"
                      input-class="tw:text-right tw:text-m-design"
                      :error-message="errors.tareWeight"
                      :error="!!errors.tareWeight"
                      :mask="{
                      mask: Number, // Chỉ chấp nhận số
                      thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                      scale: 2, // Không cho phép số thập phân
                      signed: false, // Không cho phép số âm
                      min: 0, // Chỉ cho phép số không âm
                      lazy: false, // Hiển thị placeholder ngay lập tức
                      radix: '.',
                      max: 99999.99,
                      padFractionalZeros: false,
                      normalizeZeros: true,
                    }"
                    >
                      <template v-slot:append>
                        <div
                          :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                        >
                          g
                        </div>
                      </template>
                    </BaseInput>
                  </div>
                </div>
                <!-- Divider -->
                <div class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2 tw:md:hidden"/>
                <div class="tw:tl:col-span-2 tw:flex tw:items-center tw:justify-between tw:md:justify-start tw:gap-4">
                  <!-- 出荷量 -->
                  <span class="tw:text-xs-design">出荷量</span>
                  <div class="">
                    <span class="tw:text-m-design">
                      {{ form.netWeight }}
                    </span>
                    <span class="tw:text-xs-design"> g </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
            tw:border tw:border-t-0 tw:border-[#E0E0E0]">
            <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
              <span>
                出荷日
              </span>
              <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
                必須
              </q-badge>
            </div>
            <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-[4rem] tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
              <BaseDatePicker
                v-model="form.date"
                class="tw:max-w-[22rem] tw:tl:transform tw:tl:translate-y-0.5"
                input-class="tw:text-m-design tw:text-[#333333]"
                :error-message="errors.date"
                :error="!!errors.date"
              />
            </div>
          </div>
        </div>
      </q-card>

      <q-footer
        elevated
        class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
        tw:w-full tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
      >
        <BaseButton
          outline

          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
          tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:min-w-[18.9rem]`"
          label="修正をやめる"
          @click="handleClickCancel"
        />
        <BaseButton

          class="tw:rounded-[40px]"
          :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
          tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:min-w-[18.9rem]`"
          label="確認する"
          @click="handleClickConfirm"
        />
      </q-footer>
    </q-page>
    <PopupConfirmText />
    <OutboundShipmentPDF />
    <ExportShipmentPDF />
  </div>
</template>
<script setup>
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import {
  FORMAT_DATE,
  FORMAT_NUMBER,
  doParseFloatNumber,
  isNumeric,
  maskCodeString,
  GENERATE_CODE_SUFFIX,
  CHECK_ROLE,
  FORMAT_DATE_TIME_CSV,
} from 'helpers/common';
import { storeToRefs } from 'pinia';
import shippingService from 'services/shipping.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import {
  TYPE_DIFFERENCE_WEIGHT_ENUM,
  UNIT_TYPE_SETTING_ENUM,
  OPTION_TYPE_ENUM,
  ENTERPRISE_TYPE_ENUM,
  SHIPPING_TYPE_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  REPORT_TYPE_ENUM,
  ROLES_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import { useAppStore } from 'stores/app-store';
import { onMounted, ref, provide, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import editShipping from 'src/schemas/editShipping';
import BasePartnerSelect from 'src/components/base/vs/BasePartnerSelect.vue';
import commonService from 'services/common.service';
import dayjs from 'boot/dayjs';
import systemSettingsAdminService from 'src/shared/services/admin/systemSettings.admin.service';
import MESSAGE from 'helpers/message';
import toast from 'utilities/toast';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import html2pdf from 'html2pdf.js';
import OutboundShipmentPDF from 'components/pdf/OutboundShipmentPDF.vue';
import ExportShipmentPDF from 'components/pdf/ExportShipmentPDF.vue';
import { makeShippingInfoXML } from 'src/boot/print';

import PopupConfirm from './components/PopupConfirm.vue';

// #region states
const { settingUser } = storeToRefs(useAppStore());
const { validateData, errors } = useValidate();
const router = useRouter();
const data = ref({});
const listPartner = ref([]);
const optionsPartner = ref([]);
const weightAlertThreshold = ref(0);

const form = ref({
  destinationId: '',
  grossWeight: '',
  netWeight: '',
  tareWeight: '',
  quantity: '',
  date: '',
  volumeType: UNIT_TYPE_SETTING_ENUM.WEIGHT_ONLY,
  typeDiff: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR,
  reasonDiff: '',
});
const isShowPopupConfirmVolume = ref(false);
const isShowPopupConfirmReason = ref(false);
const unitPerGram = ref(settingUser.value?.unit_per_gram ?? 0);
const reasonOptions = ref([
  { label: '計量誤差', value: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR },
  { label: 'その他', value: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER },
]);
const destinationForegin = ref('');
const isPopupConformTextPopup = ref(false);
const dataExport = ref({});
const exportShipmentPDFProvideData = ref({
  rootOrigins: [],
  distributionOrigins: [],
  catchingOrigins: [],
});

// #endregion

// #region functions
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(
    form.value.tareWeight || 0
  );
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue
    ? FORMAT_NUMBER(grossWeightNum)
    : newValue;

  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity = netWeightNum >= 0
    ? FORMAT_NUMBER(
      Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
    )
    : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(
    form.value.grossWeight || 0
  );
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue
    ? FORMAT_NUMBER(tareWeightNum)
    : newValue;

  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity = netWeightNum >= 0
    ? FORMAT_NUMBER(
      Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
    )
    : undefined;
};

const handleClickConfirm = async () => {
  const validate = validateData(editShipping, form.value);
  if (!validate) {
    return;
  }

  isShowPopupConfirmVolume.value = true;
};

const handleClickSubmitVolume = async () => {
  isShowPopupConfirmVolume.value = false;

  // check difference weight
  const netWeightDiffList = [];

  // calc condition to show popup reason for total net weight
  const netWeight = doParseFloatNumber(form.value.netWeight);
  const totalNetWeightSelected = +data.value.shipping_net_weight;
  const netWeightDiff = netWeight / totalNetWeightSelected;
  // if total net weight is over limit weight or less than limit weight => show popup reason
  const compareNetWeightDiff =
    netWeightDiff > weightAlertThreshold.value / 100 + 1 ||
    netWeightDiff < 1 - weightAlertThreshold.value / 100;

  netWeightDiffList.push(compareNetWeightDiff);

  if (netWeightDiffList.some(diff => diff)) {
    form.value.reasonDiff = '';
    form.value.typeDiff = TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR;
    isShowPopupConfirmReason.value = true;
  } else {
    const dataEdit = {
      destinationId: form.value.destinationId,
      shippingDate: form.value.date,
      shippingGrossWeight: doParseFloatNumber(form.value.grossWeight),
      shippingTareWeight: doParseFloatNumber(form.value.tareWeight) || undefined,
      shippingQuantity: doParseFloatNumber(form.value.quantity),
      typeDiff: form.value.typeDiff || undefined,
      reasonDiff: form.value.reasonDiff || undefined,
    };
    const result = await shippingService.editShipping(router.currentRoute.value.params?.id, dataEdit);
    if (result.code !== 0) {
      toast.error(result.message);
      return;
    }
    if (data.value.shipping_net_weight !== doParseFloatNumber(form.value.netWeight) ||
     data.value.destination_user_id !== form.value.destinationId ||
       dayjs(data.value.shipping_date).format('YYMMDD') !== dayjs(form.value.date).format('YYMMDD')
    ) {
      isPopupConformTextPopup.value = true;
    }
    if (result.code === 0 && !isPopupConformTextPopup.value) {
      toast.access(MESSAGE.MSG_FIXED_SHIPPING_INFO);
      await router.push({
        name: 'shippingList',
      });
    }
  }

};

const handleClickCancel = () => {
  router.back();
};

const checkDestinationForegin = () => data.value.destination_enterprise?.type
  === ENTERPRISE_TYPE_ENUM.FOREIGN && data.value.shipping_type === SHIPPING_TYPE_ENUM.NORMAL;

const mappingData = (shipping = {}, lstPdf = []) => {
  const { shippingInfo } = shipping;
  lstPdf.push(shipping);
  const lst = [];
  if (!shippingInfo) {
    return;
  }
  shippingInfo.forEach(value => {
    mappingData(value, lst);
  });

  if (lst.length) {
    lstPdf.push(lst);
  }
};

const getDataExport = async () => {
  const result = await shippingService.exportShippingDetail({
    id: router.currentRoute.value.params.id,
  });
  if (!result.payload.foreign_flag) {
    const isShowQrCode = !result.payload.arrival_date;
    if (settingUser.value.report_type === REPORT_TYPE_ENUM.PDF_FILE) {
      dataExport.value = {
        shipping_date: FORMAT_DATE(result.payload.shipping_date),
        code: result.payload.code,
        starting_enterprise: CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          result.payload.starting_user
        )
          ? result.payload.starting_user?.name
          : `${result.payload.starting_user?.name}（${result.payload.starting_enterprise?.enterprise_name}）`,
        destination_enterprise:
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
            [],
            result.payload.destination_user
          ) ||
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
            [STAFF_TYPE_ENUM.ENTERPRISE],
            result.payload.destination_user
          )
            ? result.payload.destination_user?.name
            : `${result.payload.destination_user?.name}（${result.payload.destination_enterprise?.enterprise_name}）`,
        weight: settingUser.value.display_shipment_weight
          ? result.payload.shipping_net_weight
          : undefined,
        qr_code: isShowQrCode ? result.payload.qr_code : undefined,
      };

      nextTick(async () => {
        const pdfFileData = document.getElementById('shipment-pdf-file-data');
        html2pdf()
          .from(pdfFileData)
          .set({
            filename: `出荷情報_${FORMAT_DATE_TIME_CSV()}.pdf`,
            margin: [0.5, 1.25],
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 4 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'landscape' },
          })
          .save();
        toast.access(MESSAGE.MSG_DOWNLOAD_SHIPPINGPDF_INFO);
      });
    } else {
      const dataPrint = makeShippingInfoXML(
        result.payload.destination_enterprise.enterprise_name,
        result.payload.destination_user.name,
        maskCodeString(result.payload.code?.replaceAll('-', '')),
        FORMAT_NUMBER(result.payload.shipping_net_weight),
        // map utc to local time
        dayjs(FORMAT_DATE(result.payload.shipping_date)).toDate(),
        isShowQrCode ? `${result.payload.qr_code}` : undefined,
        result.payload.starting_enterprise.enterprise_name,
        result.payload.starting_user.name,
        1
      );

      const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

      const aTag = document.createElement('a');
      aTag.href = href;
      aTag.click();
    }
  } else {
    const lstPdf = [];
    mappingData(result.payload, lstPdf);
    lstPdf.forEach(value => {
      if (value.shippingInfo) {
        value.sum = value.shippingInfo.reduce((acc, curr) => +acc + (+Number(curr) || 0), 0);
      }
    });
    exportShipmentPDFProvideData.value = {
      distributionOrigins: [lstPdf],
    };

    nextTick(async () => {
      const pdfFileData = document.getElementById('export-shipment-pdf-file-data');
      html2pdf()
        .from(pdfFileData)
        .set({
          filename: `輸出向け取引記録_${FORMAT_DATE_TIME_CSV()}.pdf`,
          margin: [0.5, 0.3, 0.6, 0.3],
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 4 },
          jsPDF: { unit: 'in', format: 'a3', orientation: 'landscape' },
          pagebreak: { mode: ['avoid-all', 'css', 'legacy'] },
        }).save();
      toast.access(MESSAGE.MSG_DOWNLOAD_EXPORTPDF_INFO);
    });
  }
};

const handleClickSubmitReason = async () => {
  const dataEdit = {
    destinationId: form.value.destinationId,
    shippingDate: form.value.date,
    shippingGrossWeight: doParseFloatNumber(form.value.grossWeight),
    shippingTareWeight: doParseFloatNumber(form.value.tareWeight) || undefined,
    shippingQuantity: doParseFloatNumber(form.value.quantity),
    typeDiff: form.value.typeDiff || undefined,
    reasonDiff: form.value.reasonDiff || undefined,
  };
  const result = await shippingService.editShipping(router.currentRoute.value.params?.id, dataEdit);
  if (result.code !== 0) {
    toast.error(result.message);
  };

  if (result.code === 0) {
    toast.access(MESSAGE.MSG_FIXED_SHIPPING_INFO);
    isPopupConformTextPopup.value = true;
  }
  isShowPopupConfirmReason.value = false;
};

// for confirm text popup
const popupConfirmTextProvideData = {
  isPopup: isPopupConformTextPopup,
  titlePopup: '確認',
  caption: '輸出向け取引記録PDFをダウンロードしますか？',
  handleCloseModal: () => {
    isPopupConformTextPopup.value = false;
    router.push({
      name: 'shippingList',
    });
  },
  handleAcceptModal: async () => {
    await getDataExport();
    isPopupConformTextPopup.value = false;
    await router.push({
      name: 'shippingList',
    });
  },
};
// #endregion

// #region watch
watch(
  () => form.value.date,
  async () => {
    if (dayjs(form.value.date, 'YYYY/MM/DD', true).isValid()) {
      const codeSuffixResponse = await GENERATE_CODE_SUFFIX(data.value.code.slice(0, -3));
      data.value.code =
        data.value.code.substring(0, 7) + dayjs(form.value.date).format('YYMMDD') + codeSuffixResponse.code_suffix;
    }
  }
);
// #endregion watch

onMounted(async () => {
  const shippingDetail = await shippingService.getShippingDetail(router.currentRoute.value.params?.id);
  if (shippingDetail.code !== 0 && shippingDetail.code !== 401) {
    await router.push({
      name: 'home',
    });
  }

  // get setting admin
  const systemSettingResponse = await systemSettingsAdminService.getSystemSettingsForNormalUser();
    weightAlertThreshold.value =
      systemSettingResponse.payload[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD];

  //
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SHIPPER,
  });
  optionsPartner.value = partnerOptionsResponse.payload;
  listPartner.value = partnerOptionsResponse.payload;

  data.value = shippingDetail.payload;
  popupConfirmTextProvideData.caption = data.value.destination_enterprise.type
  === ENTERPRISE_TYPE_ENUM.FOREIGN ?
  '輸出向け取引記録PDFをダウンロードしますか？' : '出荷情報PDFをダウンロードしますか？';
  form.value = {
    destinationId: data.value.destination_user_id ? data.value.destination_user_id : '',
    grossWeight: FORMAT_NUMBER(data.value.shipping_gross_weight),
    tareWeight: FORMAT_NUMBER(data.value.shipping_tare_weight),
    netWeight: data.value.shipping_net_weight ? FORMAT_NUMBER(data.value.shipping_net_weight):
    FORMAT_NUMBER((+(data.value.shipping_gross_weight || 0) - +(data.value.shipping_tare_weight|| 0))),
    quantity: FORMAT_NUMBER(data.value.shipping_quantity),
    date: FORMAT_DATE(data.value.shipping_date),
  };

  const shipperObject = optionsPartner.value.find(item => item.value === form.value.destinationId);
  destinationForegin.value = shipperObject?.label;
  checkDestinationForegin();
});

provide('basePartnerSelectProvideData', { listPartner: optionsPartner });
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
provide('outBoundShipmentPDFProvideData', dataExport);
provide('exportShipmentPDFProvideData', exportShipmentPDFProvideData);
</script>

<style scoped>
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}
</style>
